<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <style>
        img{
            width: 100px;
            height: 100px;
        }
    </style>
</head>

<body>
    <!-- 方法1: 使用file://协议 (仅在本地文件打开时有效) -->
    <img src="file:///Z:/foods/kiwi.jpg" alt="猕猴桃">

    <!-- 方法2: 文件选择器 - 用户手动选择图片 -->
    <div>
        <h3>选择单个图片文件：</h3>
        <input type="file" id="imageInput" accept="image/*">
        <br><br>
        <img id="previewImage" src="" alt="预览图片" style="display: none;">
    </div>

    <!-- 方法3: 拖拽上传区域 -->
    <div id="dropZone" style="border: 2px dashed #ccc; padding: 20px; margin: 20px 0; text-align: center; cursor: pointer;">
        <h3>拖拽图片文件到这里或点击选择文件</h3>
        <input type="file" id="dragInput" accept="image/*" style="display: none;">
        <img id="dragPreview" src="" alt="拖拽预览" style="display: none; margin-top: 10px;">
    </div>

    <!-- 方法4: 批量加载多个图片 -->
    <div>
        <h3>选择多个图片：</h3>
        <input type="file" id="multipleImages" accept="image/*" multiple>
        <div id="imageGallery" style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;"></div>
    </div>

    <script>
        // 方法2: 单个文件选择器
        document.getElementById('imageInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.getElementById('previewImage');
                    img.src = e.target.result;
                    img.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        // 方法3: 拖拽功能
        const dropZone = document.getElementById('dropZone');
        const dragInput = document.getElementById('dragInput');
        const dragPreview = document.getElementById('dragPreview');

        dropZone.addEventListener('click', () => dragInput.click());

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.style.backgroundColor = '#f0f0f0';
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.style.backgroundColor = '';
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.style.backgroundColor = '';
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                displayImage(files[0], dragPreview);
            }
        });

        dragInput.addEventListener('change', (e) => {
            if (e.target.files[0]) {
                displayImage(e.target.files[0], dragPreview);
            }
        });

        // 方法4: 多个图片选择
        document.getElementById('multipleImages').addEventListener('change', function(event) {
            const files = event.target.files;
            const gallery = document.getElementById('imageGallery');
            gallery.innerHTML = ''; // 清空之前的图片

            for (let i = 0; i < files.length; i++) {
                if (files[i].type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.style.width = '100px';
                        img.style.height = '100px';
                        img.style.objectFit = 'cover';
                        img.alt = files[i].name;
                        img.title = files[i].name; // 显示文件名
                        gallery.appendChild(img);
                    };
                    reader.readAsDataURL(files[i]);
                }
            }
        });

        // 通用显示图片函数
        function displayImage(file, imgElement) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imgElement.src = e.target.result;
                imgElement.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    </script>
</body>

</html>