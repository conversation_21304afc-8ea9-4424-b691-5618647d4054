# X轴垂直标签显示方案

## 概述
已将图表的x轴标签从水平显示改为垂直显示（90度旋转），以解决标签重叠问题并提供更好的可读性。

## 实现的功能

### 1. 垂直标签核心配置
```javascript
axisLabel: {
  rotate: 90,              // 90度垂直旋转
  verticalAlign: 'middle', // 垂直居中对齐
  align: 'right',          // 右对齐
  overflow: 'none',        // 不截断标签
}
```

### 2. 响应式字体大小
- **小屏幕 (< 600px)**: 10px
- **中屏幕 (600-800px)**: 11px  
- **大屏幕 (> 800px)**: 12px

### 3. 智能间隔显示
- **极小屏幕 (< 480px)**: 显示1/8的标签
- **其他屏幕**: 显示所有标签

### 4. 动态布局调整
- **底部边距**: 根据最长标签长度动态调整
- **标签边距**: 垂直显示专用的边距设置
- **容器高度**: 增加最小高度以容纳垂直标签

## 布局空间计算

### 底部边距策略
```javascript
getBottomMargin() {
  const maxLabelLength = Math.max(...this.chartData.map(item => item.name.length));
  
  if (this.chartWidth < 600) {
    return maxLabelLength > 4 ? "25%" : "20%";
  } else if (this.chartWidth < 800) {
    return maxLabelLength > 4 ? "22%" : "18%";
  } else {
    return maxLabelLength > 4 ? "20%" : "15%";
  }
}
```

### 容器高度调整
- **基础高度**: 280px - 400px
- **中屏幕**: 320px - 450px
- **小屏幕**: 350px - 480px

## 优势对比

### 垂直标签的优势
✅ **无重叠问题**: 垂直排列完全避免标签重叠
✅ **更好可读性**: 文字垂直排列，阅读更清晰
✅ **支持长文本**: 可以显示较长的标签文本
✅ **响应式友好**: 在各种屏幕尺寸下都表现良好
✅ **视觉整洁**: 整体布局更加整齐美观

### 与水平标签对比
| 特性 | 水平标签 | 垂直标签 |
|------|----------|----------|
| 重叠问题 | 容易重叠 | 无重叠 |
| 长文本支持 | 需要截断 | 支持更长文本 |
| 空间利用 | 水平空间紧张 | 垂直空间充足 |
| 可读性 | 标签多时较差 | 始终清晰 |
| 响应式适配 | 需要复杂逻辑 | 简单有效 |

## 配置参数说明

### 核心参数
```javascript
// 垂直标签配置
rotate: 90,                    // 旋转角度（固定90度）
verticalAlign: 'middle',       // 垂直对齐方式
align: 'right',               // 水平对齐方式
margin: 12-15,                // 标签边距
fontSize: 10-12,              // 字体大小
interval: 0 或 Math.ceil(n/8)-1 // 显示间隔
```

### 布局参数
```javascript
// 容器配置
minHeight: 280-350px,         // 最小高度
maxHeight: 400-480px,         // 最大高度
bottomMargin: 15%-25%,        // 底部边距
paddingBottom: 30-40px,       // 底部内边距
```

## 使用建议

### 适用场景
- ✅ 标签文本较长（> 4个字符）
- ✅ 数据项较多（> 8个）
- ✅ 需要在小屏幕上显示
- ✅ 要求高可读性

### 注意事项
1. **容器高度**: 确保容器有足够高度显示垂直标签
2. **文本长度**: 超长文本仍需要适当截断
3. **响应式测试**: 在不同屏幕尺寸下测试效果
4. **性能考虑**: 大量数据时考虑分页或虚拟滚动

## 自定义配置

### 调整旋转角度
如需要其他角度（如45度），可修改：
```javascript
rotate: 45,  // 45度倾斜显示
align: 'center',
verticalAlign: 'top',
```

### 调整对齐方式
```javascript
// 左对齐
align: 'left',
verticalAlign: 'middle',

// 居中对齐  
align: 'center',
verticalAlign: 'middle',
```

### 调整间距
```javascript
// 增加标签间距
margin: 20,

// 调整底部边距
bottom: "30%",
```

## 故障排除

### 常见问题
1. **标签被截断**: 增加底部边距或容器高度
2. **标签重叠**: 检查间隔设置和字体大小
3. **显示不全**: 确保容器有足够的最小高度
4. **响应式问题**: 检查媒体查询断点设置

### 调试技巧
- 使用浏览器开发者工具检查元素尺寸
- 在控制台查看 `this.chartWidth` 和计算的边距值
- 测试不同屏幕尺寸下的显示效果
- 检查ECharts的axisLabel配置是否正确应用
