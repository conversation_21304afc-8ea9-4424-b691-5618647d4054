<template>
  <div class="oepx">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分项统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="cost-structure">
        <chartBox :title="'费用结构'">
          <CarouselBtn :buttons="buttons" />
          <CostStructure />
        </chartBox>
      </div>
      <div class="motivation-box">
        <chartBox :title="'同比增减动因'">
          <CarouselBtn :buttons="buttons" />
          <Motivation />
        </chartBox>
      </div>
      <div class="analysis-box">
        <chartBox :title="'分油气田分析'">
          <ButtonBox :buttons="ogsBtns"></ButtonBox>
          <BudgetExecution />
          <!-- <CostCompar /> -->
        </chartBox>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTable from "@/components/comTable/commonTable.vue";
import CarouselBtn from "../../components/CarouselBtn.vue";
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
import ItemCard from "../../components/ItemCard.vue";
import ButtonBox from "@/views/businessAnalysis/prodEnviTrack/buttonBox.vue";
import CostStructure from "./costStructure/index.vue";
import Motivation from "./motivation/index.vue";
import BudgetExecution from "./analysisCharts/budgetExecution.vue";
import CostCompar from "./analysisCharts/costCompar.vue";
export default {
  name: "Oepx",
  components: {
    CommonTable,
    CarouselBtn,
    DatePicker,
    ItemCard,
    ButtonBox,
    Motivation,
    BudgetExecution,
    CostCompar,
    CostStructure,
  },
  data() {
    return {
      newDateValue: "",
      cardData: [
        { title: "油气总产量", value: "100" },
        { title: "天然气总产量", value: "100" },
        { title: "石油液体总产量", value: "100" },
      ],
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      ogsBtns: ["费用同比", "预算执行"],
      colums: [
        {
          label: "科目",
          prop: "subject",
        },
        {
          label: "同期完成",
          children: [
            {
              label: "本年累计",
              prop: "thisYearTotal",
            },
            {
              label: "同期预算",
              prop: "lastYearTotal",
            },
            {
              label: "差额",
              prop: "difference",
            },
            {
              label: "同期完成率",
              prop: "completionRate",
            },
          ],
        },
        {
          label: "全年完成",
          children: [
            {
              label: "去年预算",
              prop: "lastYearTotal",
            },
            {
              label: "全年完成率",
              prop: "completionRateYear",
            },
          ],
        },
      ],
      tableData: [
        {
          subject: "科目1",
          thisYearTotal: "100",
          lastYearTotal: "100",
          difference: "100",
          completionRate: "100",
          lastYearTotal: "100",
          completionRateYear: "100",
        },
        {
          subject: "科目2",
          thisYearTotal: "100",
          lastYearTotal: "100",
          difference: "100",
          completionRate: "100",
          lastYearTotal: "100",
          completionRateYear: "100",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.oepx {
  .content-up {
    display: flex;
    justify-content: space-between;

    .main-indicators {
      flex: 1;
      min-width: 0;
      margin-right: 10px;

      .card-box {
        display: flex;
        justify-content: space-between;
        margin: 8px 20px; // 减少边距，避免内容溢出
        flex: 1; // 占据剩余空间
        min-height: 0; // 允许flex收缩
        gap: 12px; // 使用gap替代margin-right

        .item-card {
          flex: 1;
          min-width: 0; // 允许收缩
          height: 100%; // 确保卡片填满容器高度
        }
      }
    }

    .statistics-box {
      flex: 1;
      min-width: 0;

      .table-box {
        margin: 12px 16px; // 减少上下边距
        flex: 1;
        overflow: visible; // 移除滚动条，让表格完整显示
        display: flex;
        flex-direction: column;
      }
    }
  }
  .content-down {
    margin-top: 6px;
    display: flex;
    justify-content: space-between;
    gap: 10px;

    .cost-structure {
      flex: 1;
      min-width: 0;
    }
    .motivation-box {
      flex: 1;
      min-width: 0;
    }
    .analysis-box {
      flex: 1;
      min-width: 0;
    }
  }
}

// 移除表格右边框
::v-deep .el-table--border::after {
  width: 0 !important;
}

::v-deep .el-table th.el-table__cell:nth-child(3) {
  border-right: none !important;
}

::v-deep .el-table th.el-table__cell:nth-child(6) {
  border-right: none !important;
}
</style>
